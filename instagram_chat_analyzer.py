#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Instagram聊天记录分析器
分析Instagram导出的聊天记录JSON文件
"""

import json
import os
import re
from datetime import datetime, timezone
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import pandas as pd
from typing import Dict, List, Any
import codecs

# 设置matplotlib支持中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class InstagramChatAnalyzer:
    def __init__(self, directory_path: str = "."):
        """
        初始化分析器
        
        Args:
            directory_path: 包含message_*.json文件的目录路径
        """
        self.directory_path = directory_path
        self.messages = []
        self.participants = set()
        self.message_files = []
        
    def find_message_files(self) -> List[str]:
        """查找所有message_*.json文件"""
        files = []
        for i in range(1, 10):  # 查找message_1.json到message_9.json
            filename = f"message_{i}.json"
            filepath = os.path.join(self.directory_path, filename)
            if os.path.exists(filepath):
                files.append(filepath)
        return sorted(files)
    
    def decode_unicode_content(self, text: str) -> str:
        """
        解码Unicode转义的文本内容
        
        Args:
            text: 包含Unicode转义序列的文本
            
        Returns:
            解码后的文本
        """
        if not text:
            return ""
        
        try:
            # 处理Instagram导出的Unicode编码
            # 先尝试直接解码
            decoded = text.encode('latin1').decode('utf-8')
            return decoded
        except (UnicodeDecodeError, UnicodeEncodeError):
            try:
                # 如果直接解码失败，尝试使用codecs.decode
                decoded = codecs.decode(text, 'unicode_escape')
                return decoded
            except:
                # 如果都失败了，返回原文本
                return text
    
    def timestamp_to_datetime(self, timestamp_ms: int) -> datetime:
        """
        将毫秒时间戳转换为datetime对象
        
        Args:
            timestamp_ms: 毫秒时间戳
            
        Returns:
            datetime对象
        """
        return datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
    
    def load_messages(self) -> None:
        """加载所有消息文件"""
        self.message_files = self.find_message_files()
        print(f"找到 {len(self.message_files)} 个消息文件")
        
        for file_path in self.message_files:
            print(f"正在加载: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 添加参与者信息
                if 'participants' in data:
                    for participant in data['participants']:
                        decoded_name = self.decode_unicode_content(participant['name'])
                        self.participants.add(decoded_name)
                
                # 处理消息
                if 'messages' in data:
                    for message in data['messages']:
                        processed_message = self.process_message(message)
                        if processed_message:
                            self.messages.append(processed_message)
                            
            except Exception as e:
                print(f"加载文件 {file_path} 时出错: {e}")
        
        print(f"总共加载了 {len(self.messages)} 条消息")
        print(f"参与者: {', '.join(self.participants)}")
    
    def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单条消息
        
        Args:
            message: 原始消息数据
            
        Returns:
            处理后的消息数据
        """
        processed = {}
        
        # 解码发送者姓名
        if 'sender_name' in message:
            processed['sender_name'] = self.decode_unicode_content(message['sender_name'])
        
        # 转换时间戳
        if 'timestamp_ms' in message:
            processed['timestamp_ms'] = message['timestamp_ms']
            processed['datetime'] = self.timestamp_to_datetime(message['timestamp_ms'])
            processed['date'] = processed['datetime'].date()
            processed['hour'] = processed['datetime'].hour
        
        # 解码消息内容
        if 'content' in message:
            processed['content'] = self.decode_unicode_content(message['content'])
            processed['content_length'] = len(processed['content'])
        else:
            processed['content'] = ""
            processed['content_length'] = 0
        
        # 处理分享内容
        if 'share' in message and 'share_text' in message['share']:
            processed['share_text'] = self.decode_unicode_content(message['share']['share_text'])
        
        # 处理反应
        if 'reactions' in message:
            processed['reactions'] = message['reactions']
            processed['reaction_count'] = len(message['reactions'])
        else:
            processed['reaction_count'] = 0
        
        # 其他字段
        processed['type'] = self.determine_message_type(message)
        
        return processed
    
    def determine_message_type(self, message: Dict[str, Any]) -> str:
        """确定消息类型"""
        if 'photos' in message:
            return '图片'
        elif 'videos' in message:
            return '视频'
        elif 'audio_files' in message:
            return '音频'
        elif 'share' in message:
            return '分享'
        elif 'content' in message:
            return '文本'
        else:
            return '其他'

    def analyze_message_frequency(self) -> Dict[str, Any]:
        """分析消息频率"""
        if not self.messages:
            return {}

        # 按发送者统计
        sender_counts = Counter(msg['sender_name'] for msg in self.messages)

        # 按日期统计
        date_counts = Counter(msg['date'] for msg in self.messages)

        # 按小时统计
        hour_counts = Counter(msg['hour'] for msg in self.messages)

        # 按消息类型统计
        type_counts = Counter(msg['type'] for msg in self.messages)

        return {
            'sender_counts': dict(sender_counts),
            'date_counts': dict(date_counts),
            'hour_counts': dict(hour_counts),
            'type_counts': dict(type_counts),
            'total_messages': len(self.messages)
        }

    def analyze_content_statistics(self) -> Dict[str, Any]:
        """分析内容统计"""
        if not self.messages:
            return {}

        text_messages = [msg for msg in self.messages if msg['content']]

        if not text_messages:
            return {'text_message_count': 0}

        # 字符长度统计
        lengths = [msg['content_length'] for msg in text_messages]

        # 词频统计（简单的中文分词）
        all_text = ' '.join(msg['content'] for msg in text_messages)

        # 反应统计
        total_reactions = sum(msg['reaction_count'] for msg in self.messages)

        return {
            'text_message_count': len(text_messages),
            'avg_message_length': sum(lengths) / len(lengths) if lengths else 0,
            'max_message_length': max(lengths) if lengths else 0,
            'min_message_length': min(lengths) if lengths else 0,
            'total_characters': sum(lengths),
            'total_reactions': total_reactions,
            'messages_with_reactions': len([msg for msg in self.messages if msg['reaction_count'] > 0])
        }

    def analyze_timeline(self) -> Dict[str, Any]:
        """分析时间线"""
        if not self.messages:
            return {}

        # 按时间排序
        sorted_messages = sorted(self.messages, key=lambda x: x['timestamp_ms'])

        first_message = sorted_messages[0]
        last_message = sorted_messages[-1]

        # 计算对话持续时间
        duration = last_message['datetime'] - first_message['datetime']

        # 按月份统计
        monthly_counts = defaultdict(int)
        for msg in self.messages:
            month_key = f"{msg['datetime'].year}-{msg['datetime'].month:02d}"
            monthly_counts[month_key] += 1

        return {
            'first_message_date': first_message['datetime'].strftime('%Y-%m-%d %H:%M:%S'),
            'last_message_date': last_message['datetime'].strftime('%Y-%m-%d %H:%M:%S'),
            'conversation_duration_days': duration.days,
            'monthly_distribution': dict(monthly_counts)
        }

    def generate_visualizations(self) -> None:
        """生成可视化图表"""
        if not self.messages:
            print("没有消息数据，无法生成图表")
            return

        freq_analysis = self.analyze_message_frequency()

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Instagram聊天记录分析', fontsize=16, fontweight='bold')

        # 1. 发送者消息数量
        if freq_analysis['sender_counts']:
            senders = list(freq_analysis['sender_counts'].keys())
            counts = list(freq_analysis['sender_counts'].values())
            axes[0, 0].bar(senders, counts, color=['#E1306C', '#405DE6'])
            axes[0, 0].set_title('各发送者消息数量')
            axes[0, 0].set_ylabel('消息数量')

            # 添加数值标签
            for i, v in enumerate(counts):
                axes[0, 0].text(i, v + max(counts) * 0.01, str(v), ha='center')

        # 2. 消息类型分布
        if freq_analysis['type_counts']:
            types = list(freq_analysis['type_counts'].keys())
            type_counts = list(freq_analysis['type_counts'].values())
            axes[0, 1].pie(type_counts, labels=types, autopct='%1.1f%%', startangle=90)
            axes[0, 1].set_title('消息类型分布')

        # 3. 每小时消息分布
        if freq_analysis['hour_counts']:
            hours = sorted(freq_analysis['hour_counts'].keys())
            hour_counts = [freq_analysis['hour_counts'][h] for h in hours]
            axes[1, 0].plot(hours, hour_counts, marker='o', linewidth=2, markersize=6)
            axes[1, 0].set_title('24小时消息分布')
            axes[1, 0].set_xlabel('小时')
            axes[1, 0].set_ylabel('消息数量')
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].set_xticks(range(0, 24, 2))

        # 4. 每日消息趋势
        if freq_analysis['date_counts']:
            dates = sorted(freq_analysis['date_counts'].keys())
            daily_counts = [freq_analysis['date_counts'][d] for d in dates]
            axes[1, 1].plot(dates, daily_counts, linewidth=2)
            axes[1, 1].set_title('每日消息趋势')
            axes[1, 1].set_xlabel('日期')
            axes[1, 1].set_ylabel('消息数量')
            axes[1, 1].tick_params(axis='x', rotation=45)
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('instagram_chat_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图表已保存为 'instagram_chat_analysis.png'")

    def print_analysis_report(self) -> None:
        """打印详细的分析报告"""
        print("\n" + "="*60)
        print("📱 INSTAGRAM 聊天记录分析报告")
        print("="*60)

        if not self.messages:
            print("❌ 没有找到消息数据")
            return

        # 基本统计
        freq_analysis = self.analyze_message_frequency()
        content_analysis = self.analyze_content_statistics()
        timeline_analysis = self.analyze_timeline()

        print(f"\n📊 基本统计信息:")
        print(f"   • 总消息数量: {freq_analysis['total_messages']:,} 条")
        print(f"   • 参与者数量: {len(self.participants)} 人")
        print(f"   • 参与者: {', '.join(self.participants)}")
        print(f"   • 对话持续时间: {timeline_analysis.get('conversation_duration_days', 0)} 天")
        print(f"   • 首条消息时间: {timeline_analysis.get('first_message_date', 'N/A')}")
        print(f"   • 最后消息时间: {timeline_analysis.get('last_message_date', 'N/A')}")

        print(f"\n💬 发送者统计:")
        for sender, count in freq_analysis['sender_counts'].items():
            percentage = (count / freq_analysis['total_messages']) * 100
            print(f"   • {sender}: {count:,} 条 ({percentage:.1f}%)")

        print(f"\n📝 内容分析:")
        print(f"   • 文本消息: {content_analysis.get('text_message_count', 0):,} 条")
        print(f"   • 平均消息长度: {content_analysis.get('avg_message_length', 0):.1f} 字符")
        print(f"   • 最长消息: {content_analysis.get('max_message_length', 0)} 字符")
        print(f"   • 总字符数: {content_analysis.get('total_characters', 0):,} 字符")
        print(f"   • 总反应数: {content_analysis.get('total_reactions', 0)} 个")
        print(f"   • 有反应的消息: {content_analysis.get('messages_with_reactions', 0)} 条")

        print(f"\n📱 消息类型分布:")
        for msg_type, count in freq_analysis['type_counts'].items():
            percentage = (count / freq_analysis['total_messages']) * 100
            print(f"   • {msg_type}: {count:,} 条 ({percentage:.1f}%)")

        print(f"\n⏰ 活跃时间分析:")
        hour_counts = freq_analysis['hour_counts']
        if hour_counts:
            most_active_hour = max(hour_counts.items(), key=lambda x: x[1])
            print(f"   • 最活跃时间: {most_active_hour[0]}:00 ({most_active_hour[1]} 条消息)")

            # 时间段分析
            morning = sum(hour_counts.get(h, 0) for h in range(6, 12))
            afternoon = sum(hour_counts.get(h, 0) for h in range(12, 18))
            evening = sum(hour_counts.get(h, 0) for h in range(18, 24))
            night = sum(hour_counts.get(h, 0) for h in range(0, 6))

            print(f"   • 上午 (6-12点): {morning} 条")
            print(f"   • 下午 (12-18点): {afternoon} 条")
            print(f"   • 晚上 (18-24点): {evening} 条")
            print(f"   • 深夜 (0-6点): {night} 条")

        print(f"\n📅 月度分布:")
        monthly_dist = timeline_analysis.get('monthly_distribution', {})
        for month, count in sorted(monthly_dist.items()):
            print(f"   • {month}: {count:,} 条消息")

        print("\n" + "="*60)
        print("✅ 分析完成！")
        print("="*60)

    def export_to_csv(self, filename: str = "instagram_messages.csv") -> None:
        """导出消息到CSV文件"""
        if not self.messages:
            print("没有消息数据可导出")
            return

        # 准备数据
        export_data = []
        for msg in self.messages:
            export_data.append({
                '发送者': msg['sender_name'],
                '时间': msg['datetime'].strftime('%Y-%m-%d %H:%M:%S'),
                '内容': msg['content'][:100] + '...' if len(msg['content']) > 100 else msg['content'],
                '消息类型': msg['type'],
                '字符长度': msg['content_length'],
                '反应数': msg['reaction_count']
            })

        # 创建DataFrame并导出
        df = pd.DataFrame(export_data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"消息数据已导出到 '{filename}'")

    def find_most_common_words(self, top_n: int = 20) -> List[tuple]:
        """查找最常用的词汇（简单实现）"""
        if not self.messages:
            return []

        # 收集所有文本内容
        all_text = ' '.join(msg['content'] for msg in self.messages if msg['content'])

        # 简单的词汇提取（去除标点符号和空格）
        import re
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', all_text.lower())

        # 过滤掉单字符的词
        words = [word for word in words if len(word) > 1]

        word_counts = Counter(words)
        return word_counts.most_common(top_n)

    def analyze_conversation_patterns(self) -> Dict[str, Any]:
        """分析对话模式"""
        if not self.messages:
            return {}

        # 按时间排序
        sorted_messages = sorted(self.messages, key=lambda x: x['timestamp_ms'])

        # 分析回复时间
        reply_times = []
        conversation_switches = 0

        for i in range(1, len(sorted_messages)):
            current_msg = sorted_messages[i]
            prev_msg = sorted_messages[i-1]

            # 计算回复时间（分钟）
            time_diff = (current_msg['timestamp_ms'] - prev_msg['timestamp_ms']) / (1000 * 60)
            reply_times.append(time_diff)

            # 检查对话切换（发送者变化）
            if current_msg['sender_name'] != prev_msg['sender_name']:
                conversation_switches += 1

        # 计算统计数据
        avg_reply_time = sum(reply_times) / len(reply_times) if reply_times else 0

        return {
            'conversation_switches': conversation_switches,
            'avg_reply_time_minutes': avg_reply_time,
            'max_reply_time_minutes': max(reply_times) if reply_times else 0,
            'min_reply_time_minutes': min(reply_times) if reply_times else 0
        }


def main():
    """主函数"""
    print("🚀 Instagram聊天记录分析器启动中...")

    # 创建分析器实例
    analyzer = InstagramChatAnalyzer()

    # 加载消息
    print("\n📂 正在加载消息文件...")
    analyzer.load_messages()

    if not analyzer.messages:
        print("❌ 没有找到有效的消息数据")
        return

    # 执行分析
    print("\n🔍 正在进行分析...")

    # 打印详细报告
    analyzer.print_analysis_report()

    # 分析对话模式
    print("\n💭 对话模式分析:")
    patterns = analyzer.analyze_conversation_patterns()
    print(f"   • 对话切换次数: {patterns.get('conversation_switches', 0)}")
    print(f"   • 平均回复时间: {patterns.get('avg_reply_time_minutes', 0):.1f} 分钟")
    print(f"   • 最长回复时间: {patterns.get('max_reply_time_minutes', 0):.1f} 分钟")

    # 常用词汇分析
    print("\n📝 最常用词汇 (前10个):")
    common_words = analyzer.find_most_common_words(10)
    for i, (word, count) in enumerate(common_words, 1):
        print(f"   {i:2d}. {word}: {count} 次")

    # 生成可视化图表
    print("\n📊 正在生成可视化图表...")
    try:
        analyzer.generate_visualizations()
    except Exception as e:
        print(f"生成图表时出错: {e}")
        print("可能需要安装matplotlib: pip install matplotlib")

    # 导出数据
    print("\n💾 正在导出数据...")
    try:
        analyzer.export_to_csv()
    except Exception as e:
        print(f"导出CSV时出错: {e}")
        print("可能需要安装pandas: pip install pandas")

    print("\n🎉 分析完成！")
    print("\n📋 生成的文件:")
    print("   • instagram_chat_analysis.png - 分析图表")
    print("   • instagram_messages.csv - 消息数据")


if __name__ == "__main__":
    main()
