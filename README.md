# Instagram聊天记录分析器

这是一个用于分析Instagram导出聊天记录的Python工具，可以处理Unicode编码的中文内容并提供详细的统计分析。

## 功能特点

- ✅ 自动解码Instagram导出的Unicode转义中文文本
- ✅ 时间戳转换和时间线分析
- ✅ 发送者统计和消息频率分析
- ✅ 消息类型分布（文本、图片、视频、分享等）
- ✅ 活跃时间段分析
- ✅ 对话模式和回复时间分析
- ✅ 常用词汇统计
- ✅ 可视化图表生成
- ✅ CSV数据导出

## 文件说明

### 主要脚本

1. **`instagram_chat_analyzer.py`** - 完整功能版本
   - 包含所有分析功能
   - 生成可视化图表
   - 导出CSV数据
   - 需要安装matplotlib和pandas

2. **`simple_instagram_analyzer.py`** - 简化版本
   - 只使用Python标准库
   - 提供文本形式的分析报告
   - 无需安装额外依赖

### 配置文件

- **`requirements.txt`** - 完整版本所需的Python包依赖

## 安装和使用

### 方法1：使用完整功能版本

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行分析：
```bash
python instagram_chat_analyzer.py
```

### 方法2：使用简化版本（推荐新手）

直接运行，无需安装额外依赖：
```bash
python simple_instagram_analyzer.py
```

## 数据格式要求

脚本会自动查找当前目录下的以下文件：
- `message_1.json`
- `message_2.json`
- `message_3.json`
- `message_4.json`
- `message_5.json`
- `message_6.json`

这些文件应该是从Instagram导出的聊天记录JSON文件。

## 输出结果

### 完整版本输出：
- 详细的文本分析报告
- `instagram_chat_analysis.png` - 可视化图表
- `instagram_messages.csv` - 消息数据CSV文件

### 简化版本输出：
- 详细的文本分析报告

## 分析内容包括

### 📊 基本统计信息
- 总消息数量
- 参与者信息
- 对话持续时间
- 首末消息时间

### 💬 发送者统计
- 各发送者消息数量和占比

### 📝 内容分析
- 文本消息统计
- 平均/最长消息长度
- 总字符数
- 反应统计

### ⏰ 活跃时间分析
- 最活跃时间段
- 24小时分布
- 时间段统计（上午/下午/晚上/深夜）

### 📱 消息类型分布
- 文本、图片、视频、分享等类型统计

### 💭 对话模式分析
- 对话切换次数
- 平均回复时间
- 最长回复时间

### 📝 词汇分析
- 最常用词汇统计

## 注意事项

1. **编码处理**：脚本会自动处理Instagram导出的Unicode转义编码
2. **时区**：时间戳会转换为UTC时间
3. **文件路径**：确保JSON文件在脚本运行目录下
4. **中文支持**：完整支持中文内容的分析和显示

## 故障排除

### 如果遇到编码问题：
- 确保JSON文件是UTF-8编码
- 检查文件是否完整下载

### 如果图表无法显示：
- 安装matplotlib：`pip install matplotlib`
- 或使用简化版本：`python simple_instagram_analyzer.py`

### 如果CSV导出失败：
- 安装pandas：`pip install pandas`
- 或查看控制台的文本报告

## 示例输出

```
📱 INSTAGRAM 聊天记录分析报告
============================================================

📊 基本统计信息:
   • 总消息数量: 1,234 条
   • 参与者数量: 2 人
   • 参与者: Meng, 🥳0️⃣
   • 对话持续时间: 45 天

💬 发送者统计:
   • Meng: 856 条 (69.4%)
   • 🥳0️⃣: 378 条 (30.6%)

📝 内容分析:
   • 文本消息: 1,156 条
   • 平均消息长度: 23.4 字符
   • 最长消息: 245 字符

⏰ 活跃时间分析:
   • 最活跃时间: 20:00 (89 条消息)
   • 晚上 (18-24点): 456 条
```

## 许可证

此项目仅供学习和个人使用。请遵守Instagram的服务条款和隐私政策。
