#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Instagram聊天记录分析器
不依赖外部库，只使用Python标准库
"""

import json
import os
import re
from datetime import datetime, timezone
from collections import defaultdict, Counter
import codecs

class SimpleInstagramAnalyzer:
    def __init__(self, directory_path: str = "."):
        """初始化分析器"""
        self.directory_path = directory_path
        self.messages = []
        self.participants = set()
        
    def find_message_files(self):
        """查找所有message_*.json文件"""
        files = []
        for i in range(1, 10):
            filename = f"message_{i}.json"
            filepath = os.path.join(self.directory_path, filename)
            if os.path.exists(filepath):
                files.append(filepath)
        return sorted(files)
    
    def decode_unicode_content(self, text):
        """解码Unicode转义的文本内容"""
        if not text:
            return ""
        
        try:
            # 处理Instagram导出的Unicode编码
            decoded = text.encode('latin1').decode('utf-8')
            return decoded
        except (UnicodeDecodeError, UnicodeEncodeError):
            try:
                decoded = codecs.decode(text, 'unicode_escape')
                return decoded
            except:
                return text
    
    def timestamp_to_datetime(self, timestamp_ms):
        """将毫秒时间戳转换为datetime对象"""
        return datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
    
    def load_messages(self):
        """加载所有消息文件"""
        message_files = self.find_message_files()
        print(f"找到 {len(message_files)} 个消息文件")
        
        for file_path in message_files:
            print(f"正在加载: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 添加参与者信息
                if 'participants' in data:
                    for participant in data['participants']:
                        decoded_name = self.decode_unicode_content(participant['name'])
                        self.participants.add(decoded_name)
                
                # 处理消息
                if 'messages' in data:
                    for message in data['messages']:
                        processed_message = self.process_message(message)
                        if processed_message:
                            self.messages.append(processed_message)
                            
            except Exception as e:
                print(f"加载文件 {file_path} 时出错: {e}")
        
        print(f"总共加载了 {len(self.messages)} 条消息")
        print(f"参与者: {', '.join(self.participants)}")
    
    def process_message(self, message):
        """处理单条消息"""
        processed = {}
        
        # 解码发送者姓名
        if 'sender_name' in message:
            processed['sender_name'] = self.decode_unicode_content(message['sender_name'])
        
        # 转换时间戳
        if 'timestamp_ms' in message:
            processed['timestamp_ms'] = message['timestamp_ms']
            processed['datetime'] = self.timestamp_to_datetime(message['timestamp_ms'])
            processed['date'] = processed['datetime'].date()
            processed['hour'] = processed['datetime'].hour
        
        # 解码消息内容
        if 'content' in message:
            processed['content'] = self.decode_unicode_content(message['content'])
            processed['content_length'] = len(processed['content'])
        else:
            processed['content'] = ""
            processed['content_length'] = 0
        
        # 处理分享内容
        if 'share' in message and 'share_text' in message['share']:
            processed['share_text'] = self.decode_unicode_content(message['share']['share_text'])
        
        # 处理反应
        if 'reactions' in message:
            processed['reactions'] = message['reactions']
            processed['reaction_count'] = len(message['reactions'])
        else:
            processed['reaction_count'] = 0
        
        # 确定消息类型
        processed['type'] = self.determine_message_type(message)
        
        return processed
    
    def determine_message_type(self, message):
        """确定消息类型"""
        if 'photos' in message:
            return '图片'
        elif 'videos' in message:
            return '视频'
        elif 'audio_files' in message:
            return '音频'
        elif 'share' in message:
            return '分享'
        elif 'content' in message:
            return '文本'
        else:
            return '其他'
    
    def analyze_all(self):
        """执行完整分析"""
        if not self.messages:
            print("❌ 没有消息数据")
            return
        
        print("\n" + "="*60)
        print("📱 INSTAGRAM 聊天记录分析报告")
        print("="*60)
        
        # 基本统计
        self.print_basic_stats()
        
        # 发送者统计
        self.print_sender_stats()
        
        # 内容分析
        self.print_content_analysis()
        
        # 时间分析
        self.print_time_analysis()
        
        # 消息类型分析
        self.print_message_type_analysis()
        
        # 对话模式分析
        self.print_conversation_patterns()
        
        # 常用词汇
        self.print_common_words()
        
        print("\n" + "="*60)
        print("✅ 分析完成！")
        print("="*60)
    
    def print_basic_stats(self):
        """打印基本统计信息"""
        sorted_messages = sorted(self.messages, key=lambda x: x['timestamp_ms'])
        first_message = sorted_messages[0]
        last_message = sorted_messages[-1]
        duration = last_message['datetime'] - first_message['datetime']
        
        print(f"\n📊 基本统计信息:")
        print(f"   • 总消息数量: {len(self.messages):,} 条")
        print(f"   • 参与者数量: {len(self.participants)} 人")
        print(f"   • 参与者: {', '.join(self.participants)}")
        print(f"   • 对话持续时间: {duration.days} 天")
        print(f"   • 首条消息时间: {first_message['datetime'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   • 最后消息时间: {last_message['datetime'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    def print_sender_stats(self):
        """打印发送者统计"""
        sender_counts = Counter(msg['sender_name'] for msg in self.messages)
        
        print(f"\n💬 发送者统计:")
        for sender, count in sender_counts.items():
            percentage = (count / len(self.messages)) * 100
            print(f"   • {sender}: {count:,} 条 ({percentage:.1f}%)")
    
    def print_content_analysis(self):
        """打印内容分析"""
        text_messages = [msg for msg in self.messages if msg['content']]
        
        if text_messages:
            lengths = [msg['content_length'] for msg in text_messages]
            total_reactions = sum(msg['reaction_count'] for msg in self.messages)
            messages_with_reactions = len([msg for msg in self.messages if msg['reaction_count'] > 0])
            
            print(f"\n📝 内容分析:")
            print(f"   • 文本消息: {len(text_messages):,} 条")
            print(f"   • 平均消息长度: {sum(lengths) / len(lengths):.1f} 字符")
            print(f"   • 最长消息: {max(lengths)} 字符")
            print(f"   • 总字符数: {sum(lengths):,} 字符")
            print(f"   • 总反应数: {total_reactions} 个")
            print(f"   • 有反应的消息: {messages_with_reactions} 条")
    
    def print_time_analysis(self):
        """打印时间分析"""
        hour_counts = Counter(msg['hour'] for msg in self.messages)
        
        print(f"\n⏰ 活跃时间分析:")
        if hour_counts:
            most_active_hour = max(hour_counts.items(), key=lambda x: x[1])
            print(f"   • 最活跃时间: {most_active_hour[0]}:00 ({most_active_hour[1]} 条消息)")
            
            # 时间段分析
            morning = sum(hour_counts.get(h, 0) for h in range(6, 12))
            afternoon = sum(hour_counts.get(h, 0) for h in range(12, 18))
            evening = sum(hour_counts.get(h, 0) for h in range(18, 24))
            night = sum(hour_counts.get(h, 0) for h in range(0, 6))
            
            print(f"   • 上午 (6-12点): {morning} 条")
            print(f"   • 下午 (12-18点): {afternoon} 条")
            print(f"   • 晚上 (18-24点): {evening} 条")
            print(f"   • 深夜 (0-6点): {night} 条")
    
    def print_message_type_analysis(self):
        """打印消息类型分析"""
        type_counts = Counter(msg['type'] for msg in self.messages)
        
        print(f"\n📱 消息类型分布:")
        for msg_type, count in type_counts.items():
            percentage = (count / len(self.messages)) * 100
            print(f"   • {msg_type}: {count:,} 条 ({percentage:.1f}%)")
    
    def print_conversation_patterns(self):
        """打印对话模式分析"""
        sorted_messages = sorted(self.messages, key=lambda x: x['timestamp_ms'])
        
        reply_times = []
        conversation_switches = 0
        
        for i in range(1, len(sorted_messages)):
            current_msg = sorted_messages[i]
            prev_msg = sorted_messages[i-1]
            
            # 计算回复时间（分钟）
            time_diff = (current_msg['timestamp_ms'] - prev_msg['timestamp_ms']) / (1000 * 60)
            reply_times.append(time_diff)
            
            # 检查对话切换
            if current_msg['sender_name'] != prev_msg['sender_name']:
                conversation_switches += 1
        
        avg_reply_time = sum(reply_times) / len(reply_times) if reply_times else 0
        
        print(f"\n💭 对话模式分析:")
        print(f"   • 对话切换次数: {conversation_switches}")
        print(f"   • 平均回复时间: {avg_reply_time:.1f} 分钟")
        if reply_times:
            print(f"   • 最长回复时间: {max(reply_times):.1f} 分钟")
    
    def print_common_words(self):
        """打印常用词汇"""
        all_text = ' '.join(msg['content'] for msg in self.messages if msg['content'])
        
        # 简单的词汇提取
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', all_text.lower())
        words = [word for word in words if len(word) > 1]
        
        word_counts = Counter(words)
        common_words = word_counts.most_common(10)
        
        print(f"\n📝 最常用词汇 (前10个):")
        for i, (word, count) in enumerate(common_words, 1):
            print(f"   {i:2d}. {word}: {count} 次")


def main():
    """主函数"""
    print("🚀 简化版Instagram聊天记录分析器启动中...")
    
    # 创建分析器实例
    analyzer = SimpleInstagramAnalyzer()
    
    # 加载消息
    print("\n📂 正在加载消息文件...")
    analyzer.load_messages()
    
    if not analyzer.messages:
        print("❌ 没有找到有效的消息数据")
        return
    
    # 执行分析
    print("\n🔍 正在进行分析...")
    analyzer.analyze_all()
    
    print("\n🎉 分析完成！")


if __name__ == "__main__":
    main()
