#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Instagram聊天记录深度分析器
基于CSV文件进行深入的聊天数据分析
包含消息量分析、词云图生成、发送频率分析、主动性分析和情感分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import re
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedInstagramAnalyzer:
    def __init__(self, csv_file='instagram_messages.csv'):
        """
        初始化高级分析器

        Args:
            csv_file: CSV文件路径
        """
        self.csv_file = csv_file
        self.df = None
        self.participants = []

        # 中文停用词列表
        self.chinese_stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '他', '她', '它', '我们', '你们', '他们', '她们', '它们', '这个', '那个', '这些', '那些', '这样', '那样', '什么', '怎么', '为什么', '哪里', '哪个', '多少', '几个', '第一', '第二', '可以', '应该', '能够', '需要', '想要', '喜欢', '觉得', '认为', '知道', '明白', '理解', '记得', '忘记', '开始', '结束', '继续', '停止', '但是', '然后', '所以', '因为', '如果', '虽然', '除了', '包括', '关于', '对于', '根据', '按照', '通过', '由于', '为了', '以便', '以及', '或者', '还是', '不过', '而且', '另外', '此外', '总之', '最后', '首先', '其次', '再次', '同时', '然而', '相反', '尽管', '即使', '无论', '不管', '只要', '只有', '除非', '直到', '当', '在', '从', '向', '朝', '往', '到', '于', '在于', '位于', '处于', '用于', '适于', '便于', '有利于', '不利于', '关键在于', '问题在于', '原因在于', '目的在于', '意义在于', '价值在于', '作用在于', '影响在于', '结果在于', '效果在于', '好处在于', '坏处在于', '优点在于', '缺点在于', '特点在于', '重点在于', '难点在于', '焦点在于', '核心在于', '本质在于', '实质在于', '关键是', '问题是', '原因是', '目的是', '意义是', '价值是', '作用是', '影响是', '结果是', '效果是', '好处是', '坏处是', '优点是', '缺点是', '特点是', '重点是', '难点是', '焦点是', '核心是', '本质是', '实质是'
        }

        # 英文停用词列表
        self.english_stopwords = {
            'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your', 'yours', 'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers', 'herself', 'it', 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which', 'who', 'whom', 'this', 'that', 'these', 'those', 'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing', 'a', 'an', 'the', 'and', 'but', 'if', 'or', 'because', 'as', 'until', 'while', 'of', 'at', 'by', 'for', 'with', 'through', 'during', 'before', 'after', 'above', 'below', 'up', 'down', 'in', 'out', 'on', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now'
        }

    def load_data(self):
        """加载CSV数据"""
        try:
            print("📂 正在加载CSV文件...")
            self.df = pd.read_csv(self.csv_file, encoding='utf-8-sig')

            # 转换时间列
            self.df['时间'] = pd.to_datetime(self.df['时间'])

            # 获取参与者列表
            self.participants = self.df['发送者'].unique().tolist()

            # 添加时间相关的列
            self.df['日期'] = self.df['时间'].dt.date
            self.df['小时'] = self.df['时间'].dt.hour
            self.df['星期几'] = self.df['时间'].dt.dayofweek  # 0=周一, 6=周日
            self.df['月份'] = self.df['时间'].dt.to_period('M')
            self.df['周'] = self.df['时间'].dt.to_period('W')

            print(f"✅ 数据加载成功！")
            print(f"   • 总消息数: {len(self.df):,} 条")
            print(f"   • 参与者: {', '.join(self.participants)}")
            print(f"   • 时间范围: {self.df['时间'].min()} 到 {self.df['时间'].max()}")

        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False

        return True

    def check_dependencies(self):
        """检查依赖库"""
        missing_libs = []

        try:
            import jieba
        except ImportError:
            missing_libs.append('jieba')

        try:
            import wordcloud
        except ImportError:
            missing_libs.append('wordcloud')

        try:
            import snownlp
        except ImportError:
            missing_libs.append('snownlp')

        if missing_libs:
            print("⚠️  缺少以下依赖库:")
            for lib in missing_libs:
                print(f"   • {lib}")
            print("\n请运行以下命令安装:")
            print(f"pip install {' '.join(missing_libs)}")
            return False

        return True

    def analyze_message_volume(self):
        """1. 消息量分析"""
        print("\n" + "="*60)
        print("📊 消息量分析")
        print("="*60)

        # 按发送者统计
        sender_stats = self.df['发送者'].value_counts()
        sender_pct = (sender_stats / len(self.df) * 100).round(1)

        print("\n👥 发送者消息统计:")
        for sender in sender_stats.index:
            count = sender_stats[sender]
            pct = sender_pct[sender]
            print(f"   • {sender}: {count:,} 条 ({pct}%)")

        # 创建可视化图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('消息量分析报告', fontsize=16, fontweight='bold')

        # 1. 发送者消息数量对比
        colors = ['#E1306C', '#405DE6', '#25D366', '#FF6B35']
        bars = axes[0, 0].bar(sender_stats.index, sender_stats.values,
                             color=colors[:len(sender_stats)])
        axes[0, 0].set_title('各发送者消息数量对比')
        axes[0, 0].set_ylabel('消息数量')

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + max(sender_stats.values) * 0.01,
                           f'{int(height):,}', ha='center', va='bottom')

        # 2. 发送者占比饼图
        axes[0, 1].pie(sender_stats.values, labels=sender_stats.index, autopct='%1.1f%%',
                      colors=colors[:len(sender_stats)], startangle=90)
        axes[0, 1].set_title('消息占比分布')

        # 3. 每日消息量趋势
        daily_counts = self.df.groupby('日期').size()
        axes[1, 0].plot(daily_counts.index, daily_counts.values, linewidth=2, color='#1f77b4')
        axes[1, 0].set_title('每日消息量趋势')
        axes[1, 0].set_xlabel('日期')
        axes[1, 0].set_ylabel('消息数量')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 每月消息量对比
        monthly_counts = self.df.groupby('月份').size()
        axes[1, 1].bar(range(len(monthly_counts)), monthly_counts.values, color='#ff7f0e')
        axes[1, 1].set_title('每月消息量对比')
        axes[1, 1].set_xlabel('月份')
        axes[1, 1].set_ylabel('消息数量')
        axes[1, 1].set_xticks(range(len(monthly_counts)))
        axes[1, 1].set_xticklabels([str(m) for m in monthly_counts.index], rotation=45)

        plt.tight_layout()
        plt.savefig('消息量分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📈 图表已保存为 '消息量分析.png'")

        return sender_stats, daily_counts, monthly_counts

    def generate_wordclouds(self):
        """2. 词云图生成"""
        print("\n" + "="*60)
        print("☁️ 词云图生成")
        print("="*60)

        try:
            import jieba
            from wordcloud import WordCloud
        except ImportError as e:
            print(f"❌ 缺少必要的库: {e}")
            print("请安装: pip install jieba wordcloud")
            return

        # 过滤文本消息
        text_df = self.df[self.df['消息类型'] == '文本'].copy()
        text_df = text_df[text_df['内容'].notna() & (text_df['内容'] != '')]

        def clean_text(text):
            """清理文本"""
            if pd.isna(text) or text == '':
                return ''

            # 移除特殊字符和数字
            text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\u20000-\u2a6df\u2a700-\u2b73f\u2b740-\u2b81f\u2b820-\u2ceaf\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\ua-zA-Z\s]', '', text)

            # 中文分词
            words = jieba.lcut(text)

            # 过滤停用词和短词
            filtered_words = []
            for word in words:
                word = word.strip()
                if (len(word) > 1 and
                    word not in self.chinese_stopwords and
                    word.lower() not in self.english_stopwords and
                    not word.isdigit()):
                    filtered_words.append(word)

            return ' '.join(filtered_words)

        # 为每个参与者生成词云
        fig, axes = plt.subplots(1, len(self.participants) + 1, figsize=(6 * (len(self.participants) + 1), 6))
        if len(self.participants) == 1:
            axes = [axes]

        colors = ['#E1306C', '#405DE6', '#25D366', '#FF6B35']

        for i, participant in enumerate(self.participants):
            print(f"🔍 正在为 {participant} 生成词云...")

            # 获取该参与者的所有文本
            participant_texts = text_df[text_df['发送者'] == participant]['内容'].tolist()
            all_text = ' '.join(participant_texts)
            cleaned_text = clean_text(all_text)

            if cleaned_text:
                # 生成词云
                wordcloud = WordCloud(
                    font_path='simhei.ttf',  # 如果没有字体文件，会使用默认字体
                    width=800, height=600,
                    background_color='white',
                    max_words=100,
                    colormap='viridis',
                    relative_scaling=0.5,
                    random_state=42
                ).generate(cleaned_text)

                axes[i].imshow(wordcloud, interpolation='bilinear')
                axes[i].set_title(f'{participant} 的词云', fontsize=14, fontweight='bold')
                axes[i].axis('off')
            else:
                axes[i].text(0.5, 0.5, f'{participant}\n暂无有效文本',
                           ha='center', va='center', transform=axes[i].transAxes,
                           fontsize=12)
                axes[i].set_title(f'{participant} 的词云', fontsize=14, fontweight='bold')
                axes[i].axis('off')

        # 生成整体词云
        print("🔍 正在生成整体对话词云...")
        all_texts = text_df['内容'].tolist()
        all_text = ' '.join(all_texts)
        cleaned_text = clean_text(all_text)

        if cleaned_text:
            wordcloud = WordCloud(
                font_path='simhei.ttf',
                width=800, height=600,
                background_color='white',
                max_words=150,
                colormap='plasma',
                relative_scaling=0.5,
                random_state=42
            ).generate(cleaned_text)

            axes[-1].imshow(wordcloud, interpolation='bilinear')
            axes[-1].set_title('整体对话词云', fontsize=14, fontweight='bold')
            axes[-1].axis('off')
        else:
            axes[-1].text(0.5, 0.5, '整体对话\n暂无有效文本',
                         ha='center', va='center', transform=axes[-1].transAxes,
                         fontsize=12)
            axes[-1].set_title('整体对话词云', fontsize=14, fontweight='bold')
            axes[-1].axis('off')

        plt.tight_layout()
        plt.savefig('词云图分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("☁️ 词云图已保存为 '词云图分析.png'")

        # 统计最常用词汇
        print("\n📝 最常用词汇统计:")
        if cleaned_text:
            words = cleaned_text.split()
            word_freq = Counter(words)

            print("整体对话前20个高频词:")
            for i, (word, count) in enumerate(word_freq.most_common(20), 1):
                print(f"   {i:2d}. {word}: {count} 次")

        return word_freq if 'word_freq' in locals() else Counter()

    def analyze_sending_frequency(self):
        """3. 发送频率分析"""
        print("\n" + "="*60)
        print("⏰ 发送频率分析")
        print("="*60)

        # 按小时分析活跃度
        hourly_activity = self.df.groupby(['发送者', '小时']).size().unstack(fill_value=0)

        # 按星期几分析活跃度
        weekday_activity = self.df.groupby(['发送者', '星期几']).size().unstack(fill_value=0)
        weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

        # 计算发送间隔
        print("\n📊 发送间隔分析:")
        for participant in self.participants:
            participant_df = self.df[self.df['发送者'] == participant].sort_values('时间')
            if len(participant_df) > 1:
                time_diffs = participant_df['时间'].diff().dropna()
                avg_interval = time_diffs.mean()
                median_interval = time_diffs.median()

                print(f"   • {participant}:")
                print(f"     - 平均发送间隔: {avg_interval}")
                print(f"     - 中位数发送间隔: {median_interval}")

        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('发送频率分析报告', fontsize=16, fontweight='bold')

        # 1. 24小时活跃度热力图
        sns.heatmap(hourly_activity, annot=True, fmt='d', cmap='YlOrRd',
                   ax=axes[0, 0], cbar_kws={'label': '消息数量'})
        axes[0, 0].set_title('24小时活跃度分布')
        axes[0, 0].set_xlabel('小时')
        axes[0, 0].set_ylabel('发送者')

        # 2. 星期活跃度对比
        weekday_activity.columns = weekday_names
        weekday_activity.plot(kind='bar', ax=axes[0, 1], width=0.8)
        axes[0, 1].set_title('一周活跃度对比')
        axes[0, 1].set_xlabel('星期')
        axes[0, 1].set_ylabel('消息数量')
        axes[0, 1].legend(title='发送者')
        axes[0, 1].tick_params(axis='x', rotation=45)

        # 3. 每小时总体活跃度
        total_hourly = self.df.groupby('小时').size()
        axes[1, 0].plot(total_hourly.index, total_hourly.values, marker='o', linewidth=2, markersize=6)
        axes[1, 0].set_title('整体24小时活跃度趋势')
        axes[1, 0].set_xlabel('小时')
        axes[1, 0].set_ylabel('消息数量')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_xticks(range(0, 24, 2))

        # 4. 高频时段识别
        peak_hours = total_hourly.nlargest(5)
        low_hours = total_hourly.nsmallest(5)

        axes[1, 1].bar(peak_hours.index, peak_hours.values, color='red', alpha=0.7, label='高频时段')
        axes[1, 1].bar(low_hours.index, low_hours.values, color='blue', alpha=0.7, label='低频时段')
        axes[1, 1].set_title('高频与低频时段对比')
        axes[1, 1].set_xlabel('小时')
        axes[1, 1].set_ylabel('消息数量')
        axes[1, 1].legend()

        plt.tight_layout()
        plt.savefig('发送频率分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("⏰ 图表已保存为 '发送频率分析.png'")

        # 打印高频和低频时段
        print(f"\n🔥 高频发送时段 (前5个):")
        for hour, count in peak_hours.items():
            print(f"   • {hour}:00 - {count} 条消息")

        print(f"\n❄️ 低频发送时段 (后5个):")
        for hour, count in low_hours.items():
            print(f"   • {hour}:00 - {count} 条消息")

        return hourly_activity, weekday_activity, peak_hours, low_hours

    def analyze_conversation_initiative(self):
        """4. 主动性分析"""
        print("\n" + "="*60)
        print("🎯 主动性分析")
        print("="*60)

        # 按时间排序
        sorted_df = self.df.sort_values('时间').reset_index(drop=True)

        # 分析对话发起者
        conversation_starters = []
        reply_times = defaultdict(list)

        # 定义新对话的时间间隔阈值（30分钟）
        conversation_gap = timedelta(minutes=30)

        for i in range(len(sorted_df)):
            current_msg = sorted_df.iloc[i]

            if i == 0:
                # 第一条消息
                conversation_starters.append(current_msg['发送者'])
            else:
                prev_msg = sorted_df.iloc[i-1]
                time_diff = current_msg['时间'] - prev_msg['时间']

                if time_diff > conversation_gap:
                    # 新对话开始
                    conversation_starters.append(current_msg['发送者'])
                else:
                    # 回复消息
                    if current_msg['发送者'] != prev_msg['发送者']:
                        # 计算回复时间（分钟）
                        reply_time_minutes = time_diff.total_seconds() / 60
                        reply_times[current_msg['发送者']].append(reply_time_minutes)

        # 统计对话发起次数
        starter_counts = Counter(conversation_starters)
        total_conversations = len(conversation_starters)

        print("\n🚀 对话发起统计:")
        for sender, count in starter_counts.items():
            percentage = (count / total_conversations) * 100
            print(f"   • {sender}: {count} 次 ({percentage:.1f}%)")

        # 回复速度分析
        print("\n⚡ 回复速度分析:")
        for sender in self.participants:
            if sender in reply_times and reply_times[sender]:
                times = reply_times[sender]
                avg_reply = np.mean(times)
                median_reply = np.median(times)
                fast_replies = len([t for t in times if t <= 5])  # 5分钟内回复

                print(f"   • {sender}:")
                print(f"     - 平均回复时间: {avg_reply:.1f} 分钟")
                print(f"     - 中位数回复时间: {median_reply:.1f} 分钟")
                print(f"     - 快速回复(≤5分钟): {fast_replies}/{len(times)} ({fast_replies/len(times)*100:.1f}%)")

        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('主动性分析报告', fontsize=16, fontweight='bold')

        # 1. 对话发起者占比
        colors = ['#E1306C', '#405DE6', '#25D366', '#FF6B35']
        axes[0, 0].pie(starter_counts.values(), labels=starter_counts.keys(),
                      autopct='%1.1f%%', colors=colors[:len(starter_counts)], startangle=90)
        axes[0, 0].set_title('对话发起者占比')

        # 2. 对话发起次数对比
        axes[0, 1].bar(starter_counts.keys(), starter_counts.values(),
                      color=colors[:len(starter_counts)])
        axes[0, 1].set_title('对话发起次数对比')
        axes[0, 1].set_ylabel('发起次数')

        # 添加数值标签
        for i, (sender, count) in enumerate(starter_counts.items()):
            axes[0, 1].text(i, count + max(starter_counts.values()) * 0.01,
                           str(count), ha='center', va='bottom')

        # 3. 平均回复时间对比
        avg_reply_times = {}
        for sender in self.participants:
            if sender in reply_times and reply_times[sender]:
                avg_reply_times[sender] = np.mean(reply_times[sender])

        if avg_reply_times:
            axes[1, 0].bar(avg_reply_times.keys(), avg_reply_times.values(),
                          color=colors[:len(avg_reply_times)])
            axes[1, 0].set_title('平均回复时间对比')
            axes[1, 0].set_ylabel('回复时间 (分钟)')

            # 添加数值标签
            for i, (sender, time) in enumerate(avg_reply_times.items()):
                axes[1, 0].text(i, time + max(avg_reply_times.values()) * 0.01,
                               f'{time:.1f}', ha='center', va='bottom')

        # 4. 快速回复率对比
        fast_reply_rates = {}
        for sender in self.participants:
            if sender in reply_times and reply_times[sender]:
                times = reply_times[sender]
                fast_replies = len([t for t in times if t <= 5])
                fast_reply_rates[sender] = (fast_replies / len(times)) * 100

        if fast_reply_rates:
            axes[1, 1].bar(fast_reply_rates.keys(), fast_reply_rates.values(),
                          color=colors[:len(fast_reply_rates)])
            axes[1, 1].set_title('快速回复率对比 (≤5分钟)')
            axes[1, 1].set_ylabel('快速回复率 (%)')

            # 添加数值标签
            for i, (sender, rate) in enumerate(fast_reply_rates.items()):
                axes[1, 1].text(i, rate + max(fast_reply_rates.values()) * 0.01,
                               f'{rate:.1f}%', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('主动性分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("🎯 图表已保存为 '主动性分析.png'")

        return starter_counts, reply_times, avg_reply_times, fast_reply_rates

    def analyze_sentiment(self):
        """5. 情感分析"""
        print("\n" + "="*60)
        print("😊 情感分析")
        print("="*60)

        try:
            from snownlp import SnowNLP
        except ImportError:
            print("❌ 缺少情感分析库 snownlp")
            print("请安装: pip install snownlp")
            return

        # 过滤文本消息
        text_df = self.df[self.df['消息类型'] == '文本'].copy()
        text_df = text_df[text_df['内容'].notna() & (text_df['内容'] != '')]

        def analyze_text_sentiment(text):
            """分析文本情感"""
            if pd.isna(text) or text == '':
                return 0.5  # 中性

            try:
                s = SnowNLP(text)
                return s.sentiments
            except:
                return 0.5

        def classify_sentiment(score):
            """分类情感"""
            if score > 0.6:
                return '积极'
            elif score < 0.4:
                return '消极'
            else:
                return '中性'

        print("🔍 正在进行情感分析...")

        # 计算情感分数
        text_df['情感分数'] = text_df['内容'].apply(analyze_text_sentiment)
        text_df['情感分类'] = text_df['情感分数'].apply(classify_sentiment)

        # 按发送者统计情感
        sentiment_stats = text_df.groupby(['发送者', '情感分类']).size().unstack(fill_value=0)
        sentiment_percentages = sentiment_stats.div(sentiment_stats.sum(axis=1), axis=0) * 100

        print("\n💭 情感分布统计:")
        for sender in self.participants:
            if sender in sentiment_stats.index:
                print(f"   • {sender}:")
                for sentiment in ['积极', '中性', '消极']:
                    if sentiment in sentiment_stats.columns:
                        count = sentiment_stats.loc[sender, sentiment]
                        pct = sentiment_percentages.loc[sender, sentiment]
                        print(f"     - {sentiment}: {count} 条 ({pct:.1f}%)")

        # 情感趋势分析
        text_df['日期'] = text_df['时间'].dt.date
        daily_sentiment = text_df.groupby(['日期', '发送者'])['情感分数'].mean().unstack(fill_value=0)

        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('情感分析报告', fontsize=16, fontweight='bold')

        # 1. 情感分布饼图
        if len(self.participants) == 2:
            for i, sender in enumerate(self.participants):
                if sender in sentiment_stats.index:
                    data = sentiment_stats.loc[sender]
                    colors = ['#2ecc71', '#f39c12', '#e74c3c']  # 绿色(积极), 橙色(中性), 红色(消极)
                    axes[0, i].pie(data.values, labels=data.index, autopct='%1.1f%%',
                                  colors=colors, startangle=90)
                    axes[0, i].set_title(f'{sender} 的情感分布')

        # 2. 情感对比柱状图
        sentiment_stats.plot(kind='bar', ax=axes[1, 0], color=['#2ecc71', '#f39c12', '#e74c3c'])
        axes[1, 0].set_title('情感分布对比')
        axes[1, 0].set_ylabel('消息数量')
        axes[1, 0].legend(title='情感类型')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 3. 情感趋势图
        if not daily_sentiment.empty:
            daily_sentiment.plot(ax=axes[1, 1], linewidth=2, marker='o')
            axes[1, 1].set_title('每日情感趋势')
            axes[1, 1].set_xlabel('日期')
            axes[1, 1].set_ylabel('平均情感分数')
            axes[1, 1].legend(title='发送者')
            axes[1, 1].tick_params(axis='x', rotation=45)
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('情感分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("😊 图表已保存为 '情感分析.png'")

        # 找出最积极和最消极的消息
        print("\n🌟 最积极的消息 (前5条):")
        top_positive = text_df.nlargest(5, '情感分数')
        for idx, row in top_positive.iterrows():
            print(f"   • {row['发送者']}: {row['内容'][:50]}... (分数: {row['情感分数']:.3f})")

        print("\n😔 最消极的消息 (前5条):")
        top_negative = text_df.nsmallest(5, '情感分数')
        for idx, row in top_negative.iterrows():
            print(f"   • {row['发送者']}: {row['内容'][:50]}... (分数: {row['情感分数']:.3f})")

        return sentiment_stats, sentiment_percentages, daily_sentiment

    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("\n" + "="*80)
        print("📋 INSTAGRAM 聊天记录深度分析综合报告")
        print("="*80)

        if self.df is None:
            print("❌ 数据未加载，请先运行 load_data()")
            return

        # 基本统计信息
        total_messages = len(self.df)
        date_range = (self.df['时间'].max() - self.df['时间'].min()).days
        text_messages = len(self.df[self.df['消息类型'] == '文本'])

        print(f"\n📊 基本信息概览:")
        print(f"   • 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   • 总消息数量: {total_messages:,} 条")
        print(f"   • 对话时间跨度: {date_range} 天")
        print(f"   • 文本消息: {text_messages:,} 条 ({text_messages/total_messages*100:.1f}%)")
        print(f"   • 参与者: {', '.join(self.participants)}")

        # 消息类型统计
        type_stats = self.df['消息类型'].value_counts()
        print(f"\n📱 消息类型分布:")
        for msg_type, count in type_stats.items():
            pct = (count / total_messages) * 100
            print(f"   • {msg_type}: {count:,} 条 ({pct:.1f}%)")

        # 活跃度总结
        daily_avg = total_messages / max(date_range, 1)
        print(f"\n📈 活跃度指标:")
        print(f"   • 日均消息量: {daily_avg:.1f} 条/天")

        # 最活跃的日期
        daily_counts = self.df.groupby('日期').size()
        most_active_date = daily_counts.idxmax()
        most_active_count = daily_counts.max()
        print(f"   • 最活跃日期: {most_active_date} ({most_active_count} 条消息)")

        # 最活跃的时间
        hourly_counts = self.df.groupby('小时').size()
        most_active_hour = hourly_counts.idxmax()
        print(f"   • 最活跃时间: {most_active_hour}:00")

        print("\n" + "="*80)
        print("🎉 分析报告生成完成！")
        print("📁 生成的文件:")
        print("   • 消息量分析.png - 消息量统计图表")
        print("   • 词云图分析.png - 词云图可视化")
        print("   • 发送频率分析.png - 时间频率分析")
        print("   • 主动性分析.png - 对话主动性分析")
        print("   • 情感分析.png - 情感倾向分析")
        print("="*80)

    def run_full_analysis(self):
        """运行完整分析流程"""
        print("🚀 Instagram聊天记录深度分析器启动中...")

        # 检查依赖
        print("\n🔍 检查依赖库...")
        if not self.check_dependencies():
            print("⚠️  部分功能可能无法使用，但基础分析仍可进行")

        # 加载数据
        if not self.load_data():
            return

        try:
            # 1. 消息量分析
            print("\n🔄 开始消息量分析...")
            self.analyze_message_volume()

            # 2. 词云图生成
            print("\n🔄 开始词云图生成...")
            self.generate_wordclouds()

            # 3. 发送频率分析
            print("\n🔄 开始发送频率分析...")
            self.analyze_sending_frequency()

            # 4. 主动性分析
            print("\n🔄 开始主动性分析...")
            self.analyze_conversation_initiative()

            # 5. 情感分析
            print("\n🔄 开始情感分析...")
            self.analyze_sentiment()

            # 6. 生成综合报告
            print("\n🔄 生成综合报告...")
            self.generate_comprehensive_report()

        except Exception as e:
            print(f"❌ 分析过程中出现错误: {e}")
            print("请检查数据格式和依赖库安装情况")


def main():
    """主函数"""
    print("🎯 Instagram聊天记录深度分析器")
    print("=" * 50)

    # 检查CSV文件是否存在
    csv_file = 'instagram_messages.csv'
    import os
    if not os.path.exists(csv_file):
        print(f"❌ 找不到文件: {csv_file}")
        print("请确保CSV文件在当前目录下")
        return

    # 创建分析器实例
    analyzer = AdvancedInstagramAnalyzer(csv_file)

    # 运行完整分析
    analyzer.run_full_analysis()

    print("\n🎊 所有分析完成！请查看生成的图表文件。")


if __name__ == "__main__":
    main()